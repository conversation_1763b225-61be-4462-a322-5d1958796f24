<template>
	<view class="page">
		<u-picker :show="show" :columns="columns" @cancel="show = false" @confirm="confirmType"
			keyName="title"></u-picker>
		<u-picker :show="showCity" ref="uPicker" :loading="loading" :columns="columnsCity" @change="changeHandler"
			keyName="title" @cancel="showCity = false" @confirm="confirmCity"></u-picker>
		<view class="header" :style="'color:'+arr[status].color" v-if="status !== ''">{{arr[status].text}}</view>
		<view class="main">
			<view class="main_item">
				<view class="title"><span>*</span>法人姓名</view>
				<input type="text" v-model="form.legalPersonName" placeholder="请输入姓名">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>法人身份证号</view>
				<input type="text" v-model="form.legalPersonIdCard" placeholder="请输入身份证号">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>联系电话</view>
				<input type="text" v-model="form.legalPersonTel" placeholder="请输入联系电话">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择省市区代理</view>
				<input type="text" v-model="form.typename" placeholder="请选择代理级别" disabled @click="show = true">
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>选择区域</view>
				<input type="text" v-model="form.city" placeholder="请选择代理区域" disabled @click="showCity = true" >
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传法人身份证照片</view>
				<view class="card">
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg1"
										imgtype="legalPersonIdCardImg1" imgclass="id_card_box" text="身份证人像面"
										:imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄人像面</view>
					</view>
					<view class="card_item">
						<view class="top">
							<view class="das">
								<view class="up">
									<upload @upload="imgUpload" :imagelist="form.legalPersonIdCardImg2"
										imgtype="legalPersonIdCardImg2" imgclass="id_card_box" text="身份证国徽面"
										:imgsize="1"></upload>
								</view>
							</view>
						</view>
						<view class="bottom">拍摄国徽面</view>
					</view>
				</view>
			</view>
			<view class="main_item">
				<view class="title"><span>*</span>上传营业执照照片</view>
				<view class="big">
					<view class="top">
						<view class="das">
							<view class="up">
								<upload @upload="imgUpload" :imagelist="form.legalPersonLicense"
									imgtype="legalPersonLicense" imgclass="id_yy_box" text="营业执照" :imgsize="1">
								</upload>
							</view>
						</view>
					</view>
					<view class="bottom">
						拍摄营业执照
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view class="btn" @click="submit" v-if="status !== 1 && status !== 0">立即提交</view>
		</view>
	</view>
</template>

<script>
	// import upload from '@/user/components/upload.vue';
	export default {
		// components: {
		//     upload
		//   },
		data() {
			return {
				status:'',
				loading: false,
				applyInfo: null,
				arr: [{
					text: '信息审核中，请稍作等待',
					color: '#FE921B'
				},  {
					text: '审核成功',
					color: '#07C160'
				},{
					text: '审核失败',
					color: '#E72427'
				},],
				form: {
					typename: '省级',
					type: 1,
					city: "",
					cityId: [],
					legalPersonName: '',
					legalPersonIdCard: '',
					legalPersonTel: '',
					legalPersonIdCardImg1: [],
					legalPersonIdCardImg2: [],
					legalPersonLicense: []
				},
				showMoney: false,
				show: false,
				columns: [
					[{
						title: '省级',
						value: '1'
					}, {
						title: '市级',
						value: '2'
					}, {
						title: '区/县级代理',
						value: '3'
					}]
				],
				columnsCity: [
					[]
				],
				showCity: false
			}
		},
		watch: {
			"form.type": {
				handler(nval) {
					this.form.city = '',
						this.form.cityId = []

				}
			}
		},
		methods: {
			getcity(e) {
				this.$api.service.getCity(e).then(res => {
					console.log('getcity API response:', res);
					// 检查数据结构，兼容不同的返回格式
					let cityData = res.data || res;

					// 确保cityData是数组
					if (!Array.isArray(cityData)) {
						console.error('City data is not an array:', cityData);
						return;
					}

					// 标准化数据格式
					const normalizeData = (data) => {
						return data.map(item => {
							// 处理嵌套的children数据
							let normalizedChildren = [];
							if (item.children && Array.isArray(item.children)) {
								normalizedChildren = item.children.map(child => {
									// 如果children中还有children，继续处理
									let grandChildren = [];
									if (child.children && Array.isArray(child.children)) {
										grandChildren = child.children.map(grandChild => ({
											title: grandChild.trueName || grandChild.title || grandChild.name,
											id: grandChild.id,
											pid: grandChild.pid
										}));
									}

									return {
										title: child.trueName || child.title || child.name,
										id: child.id,
										pid: child.pid,
										children: grandChildren
									};
								});
							}

							return {
								title: item.trueName || item.title || item.name,
								id: item.id,
								pid: item.pid,
								children: normalizedChildren
							};
						});
					};

					// 处理省级数据
					const provinces = normalizeData(cityData);
					this.columnsCity[0] = provinces;
					console.log('Provinces loaded:', provinces);

					if (this.form.type > 1 && provinces.length > 0) {
						// 获取第一个省的城市数据
						const firstProvince = provinces[0];
						if (firstProvince.children && firstProvince.children.length > 0) {
							// 如果省份数据包含children，直接使用
							const cities = normalizeData(firstProvince.children);
							this.columnsCity[1] = cities;
							console.log('Cities loaded from children:', cities);

							if (this.form.type > 2 && cities.length > 0) {
								// 获取第一个城市的区县数据
								const firstCity = cities[0];
								if (firstCity.children && firstCity.children.length > 0) {
									const districts = normalizeData(firstCity.children);
									this.columnsCity[2] = districts;
									console.log('Districts loaded from children:', districts);
								}
							}
						} else {
							// 如果省份数据不包含children，通过API获取
							this.$api.service.getCity(firstProvince.id).then(res1 => {
								const cityData1 = res1.data || res1;
								if (Array.isArray(cityData1)) {
									const cities = normalizeData(cityData1);
									this.columnsCity[1] = cities;
									console.log('Cities loaded from API:', cities);

									if (this.form.type > 2 && cities.length > 0) {
										this.$api.service.getCity(cities[0].id).then(res2 => {
											const cityData2 = res2.data || res2;
											if (Array.isArray(cityData2)) {
												const districts = normalizeData(cityData2);
												this.columnsCity[2] = districts;
												console.log('Districts loaded from API:', districts);
											}
										}).catch(err => {
											console.error('Error loading districts:', err);
										});
									}
								}
							}).catch(err => {
								console.error('Error loading cities:', err);
							});
						}
					}
				}).catch(err => {
					console.error('getcity API error:', err);
					uni.showToast({
						title: '获取城市数据失败',
						icon: 'none'
					});
				})
			},
			confirmCity(Array) {
				console.log('confirmCity Array:', Array);
				console.log('columnsCity:', this.columnsCity);
				console.log('form.type:', this.form.type);

				// 根据代理类型确定需要的层级数
				const levelCount = this.form.type;

				// 构建城市名称
				const cityNames = [];
				const cityIds = [];

				for (let i = 0; i < levelCount; i++) {
					const selectedItem = Array.value[i];
					if (selectedItem) {
						// 如果有选中的项目，使用选中的
						cityNames.push(selectedItem.title);
						cityIds.push(selectedItem.id);
					} else if (this.columnsCity[i] && this.columnsCity[i][0]) {
						// 如果没有选中，使用默认的第一个
						cityNames.push(this.columnsCity[i][0].title);
						cityIds.push(this.columnsCity[i][0].id);
					}
				}

				this.form.city = cityNames.join('-');
				this.form.cityId = cityIds;

				console.log('Selected city:', this.form.city);
				console.log('Selected cityId:', this.form.cityId);
				console.log('Level count:', levelCount);

				this.showCity = false;
			},
			confirmType(Array) {
				if (Array.value[0].value == 1) {
					this.columnsCity = [
						[]
					]
				} else if (Array.value[0].value == 2) { //如果用户选择了市级
					this.columnsCity = [
						[],
						[]
					]
				} else if (Array.value[0].value == 3) { //如果用户选择了区县级
					this.columnsCity = [
						[],
						[],
						[]
					]
				}
				this.form.typename = Array.value[0].title
				this.form.type = Array.value[0].value
				this.getcity(0)
				this.show = false
			},
			changeHandler(e) {
				if (this.form.type == 1) return
				const {
					columnIndex,
					index,
					// 微信小程序无法将picker实例传出来，只能通过ref操作
					picker = this.$refs.uPicker
				} = e

				console.log('changeHandler:', { columnIndex, index, columnsCity: this.columnsCity });

				if (columnIndex === 0) {
					// 当选择省份时，更新城市列表
					const selectedProvince = this.columnsCity[0] && this.columnsCity[0][index];
					if (selectedProvince) {
						console.log('Selected province:', selectedProvince);
						if (selectedProvince.children && selectedProvince.children.length > 0) {
							// 如果有children属性，使用children数据
							const cities = selectedProvince.children;
							this.columnsCity[1] = cities;
							picker.setColumnValues(1, cities);
							console.log('Cities from children:', cities);

							if (this.form.type > 2 && cities.length > 0) {
								// 如果是区县级代理，同时更新区县列表
								const firstCity = cities[0];
								if (firstCity.children && firstCity.children.length > 0) {
									const districts = firstCity.children;
									this.columnsCity[2] = districts;
									picker.setColumnValues(2, districts);
									console.log('Districts from children:', districts);
								} else {
									// 如果第一个城市没有children，清空区县列表
									this.columnsCity[2] = [];
									picker.setColumnValues(2, []);
								}
							}
						} else {
							// 如果没有children属性，通过API获取
							console.log('Loading cities via API for province:', selectedProvince.id);
							this.$api.service.getCity(selectedProvince.id).then(res => {
								const cityData = res.data || res;
								if (Array.isArray(cityData)) {
									// 标准化城市数据
									const cities = cityData.map(item => ({
										title: item.trueName || item.title || item.name,
										id: item.id,
										pid: item.pid,
										children: item.children || []
									}));
									this.columnsCity[1] = cities;
									picker.setColumnValues(1, cities);
									console.log('Cities from API:', cities);

									if (this.form.type > 2 && cities.length > 0) {
										const firstCity = cities[0];
										if (firstCity.children && firstCity.children.length > 0) {
											const districts = firstCity.children.map(item => ({
												title: item.trueName || item.title || item.name,
												id: item.id,
												pid: item.pid
											}));
											this.columnsCity[2] = districts;
											picker.setColumnValues(2, districts);
										} else {
											// 通过API获取区县数据
											this.$api.service.getCity(firstCity.id).then(res1 => {
												const districtData = res1.data || res1;
												if (Array.isArray(districtData)) {
													const districts = districtData.map(item => ({
														title: item.trueName || item.title || item.name,
														id: item.id,
														pid: item.pid
													}));
													this.columnsCity[2] = districts;
													picker.setColumnValues(2, districts);
													console.log('Districts from API:', districts);
												}
											}).catch(err => {
												console.error('Error loading districts:', err);
											});
										}
									}
								}
							}).catch(err => {
								console.error('Error loading cities:', err);
							});
						}
					}
				} else if (columnIndex === 1) {
					if (this.form.type == 2) return
					// 当选择城市时，更新区县列表
					const selectedCity = this.columnsCity[1] && this.columnsCity[1][index];
					if (selectedCity) {
						console.log('Selected city:', selectedCity);
						if (selectedCity.children && selectedCity.children.length > 0) {
							// 如果有children属性，使用children数据
							const districts = selectedCity.children;
							this.columnsCity[2] = districts;
							picker.setColumnValues(2, districts);
							console.log('Districts from city children:', districts);
						} else {
							// 如果没有children属性，通过API获取
							console.log('Loading districts via API for city:', selectedCity.id);
							this.$api.service.getCity(selectedCity.id).then(res => {
								const districtData = res.data || res;
								if (Array.isArray(districtData)) {
									const districts = districtData.map(item => ({
										title: item.trueName || item.title || item.name,
										id: item.id,
										pid: item.pid
									}));
									this.columnsCity[2] = districts;
									picker.setColumnValues(2, districts);
									console.log('Districts from API:', districts);
								}
							}).catch(err => {
								console.error('Error loading districts:', err);
							});
						}
					}
				}
			},
			submit() {
				for (let key in this.form) {
					if (this.form[key] == '') {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						})
						return
					} else if (typeof this.form[key] == 'object' && this.form[key].length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请填写完整提交',
							duration: 1000
						})
						return
					}
				}
				let p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
				if (p.test(this.form.legalPersonIdCard) == false) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的身份证号'
					})
					return
				}
				let phoneReg = /^1[3456789]\d{9}$/
				if (!phoneReg.test(this.form.legalPersonTel)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的手机号',
						duration: 1000
					})
					return
				}
				let subForm = JSON.parse(JSON.stringify(this.form))
				
				subForm.legalPersonIdCardImg1 = subForm.legalPersonIdCardImg1[0].path
				subForm.legalPersonIdCardImg2 = subForm.legalPersonIdCardImg2[0].path
				subForm.legalPersonLicense =subForm.legalPersonLicense[0].path
				delete subForm.city
				delete subForm.typename
				console.log(subForm)
				this.$api.service.dlApply(subForm).then(res => {
					console.log(res)
					if (res.code==='200') {
						uni.showToast({
							title:res.msg,
							icon: "success",
						});
						uni.navigateTo({
							url: '/pages/apply_over'
						})
						
					}else{
						uni.showToast({
							title:res.msg,
							icon: "none",
						});
						
					}
				})

			},
			imgUpload(e) {
				let {
					imagelist,
					imgtype
				} = e;
				this.form[imgtype] = imagelist;
			},
			seeDetails() {
				this.$api.service.dlSee().then(res => {
					if (res && res.data) {
						let obj = res.data
						console.log('代理申请详情数据:', obj);
						
						// 设置状态
						this.status = obj.status
						
						// 处理图片字段
						obj.legalPersonIdCardImg1 = obj.legalPersonIdcardImg1 ? [{
							path: obj.legalPersonIdcardImg1
						}] : []
						obj.legalPersonIdCardImg2 = obj.legalPersonIdcardImg2 ? [{
							path: obj.legalPersonIdcardImg2
						}] : []
						obj.legalPersonLicense = obj.legalPersonLicense ? [{
							path: obj.legalPersonLicense
						}] : []
						
						// 映射表单字段
						this.form = {
							...this.form,
							legalPersonName: obj.legalPersonName || '',
							legalPersonIdCard: obj.legalPersonIdcard || '', // 注意字段名差异
							legalPersonTel: obj.legalPersonTel || '',
							type: obj.type || 1,
							cityId: obj.cityId ? obj.cityId.split(',') : [],
							legalPersonIdCardImg1: obj.legalPersonIdCardImg1,
							legalPersonIdCardImg2: obj.legalPersonIdCardImg2,
							legalPersonLicense: obj.legalPersonLicense
						}
						
						// 设置代理类型显示名称
						if (this.form.type && this.form.type >= 1 && this.form.type <= 3) {
							this.form.typename = this.columns[0][this.form.type-1].title
						}
						
						// 根据cityId设置城市显示名称
						if (this.form.cityId && this.form.cityId.length > 0) {
							this.setCityDisplayName()
						}
					}
				}).catch((err)=>{
					console.log('获取代理申请详情失败:', err);
				})
			},
			
			// 新增方法：根据cityId设置城市显示名称
			setCityDisplayName() {
				// 这里可以根据cityId查询对应的城市名称
				// 暂时先设置为空，等城市数据加载完成后再处理
				this.form.city = ''
			}
		},
		onLoad() {
			this.seeDetails(),
			this.getcity(0)
		},
		
	}
</script>

<style scoped lang="scss">
	.page {
		padding-bottom: 200rpx;

		.header {
			width: 750rpx;
			height: 58rpx;
			background: #FFF7F1;
			line-height: 58rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 400;
		}

		.main {
			padding: 40rpx 30rpx;

			.main_item {
				margin-bottom: 20rpx;

				.title {
					margin-bottom: 20rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #333333;

					span {
						color: #E72427;
					}
				}

				input {
					width: 690rpx;
					height: 110rpx;
					background: #F8F8F8;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 110rpx;
					padding: 0 40rpx;
					box-sizing: border-box;
				}

				.big {
					width: 690rpx;
					height: 388rpx;
					background: #F2FAFE;
					border-radius: 16rpx 16rpx 16rpx 16rpx;

					.top {
						height: 322rpx;
						padding-top: 20rpx;

						.das {
							margin: 0 auto;
							width: 632rpx;
							height: 284rpx;
							border-radius: 0rpx 0rpx 0rpx 0rpx;
							border: 2rpx dashed #2E80FE;
							padding-top: 14rpx;

							.up {
								margin: 0 auto;
								width: 594rpx;
								height: 258rpx;
								background: rgba(0, 0, 0, 0.4);
								border-radius: 12rpx 12rpx 12rpx 12rpx;
							}
						}
					}

					.bottom {
						height: 66rpx;
						width: 690rpx;
						height: 66rpx;
						background: #2E80FE;
						border-radius: 0rpx 0rpx 16rpx 16rpx;
						font-size: 28rpx;
						font-weight: 400;
						color: #FFFFFF;
						line-height: 66rpx;
						text-align: center;
					}
				}

				.card {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.card_item {
						width: 332rpx;
						height: 332rpx;
						background: #F2FAFE;
						border-radius: 16rpx 16rpx 16rpx 16rpx;
						overflow: hidden;

						.top {
							height: 266rpx;
							width: 100%;
							padding-top: 40rpx;

							.das {
								margin: 0 auto;
								width: 266rpx;
								height: 180rpx;
								border: 2rpx dashed #2E80FE;
								padding-top: 28rpx;

								.up {
									margin: 0 auto;
									width: 210rpx;
									height: 130rpx;
								}
							}
						}

						.bottom {
							height: 66rpx;
							width: 100%;
							background-color: #2E80FE;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							text-align: center;
							line-height: 66rpx;
						}
					}
				}
			}
		}

		.footer {
			padding: 52rpx 30rpx;
			width: 750rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
			position: fixed;
			bottom: 0;

			.btn {
				width: 690rpx;
				height: 98rpx;
				background: #2E80FE;
				border-radius: 50rpx 50rpx 50rpx 50rpx;
				font-size: 32rpx;
				font-weight: 500;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
			}
		}
	}
</style>